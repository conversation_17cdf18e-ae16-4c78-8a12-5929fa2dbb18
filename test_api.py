#!/usr/bin/env python3
"""
Test script for the FastAPI Image Processing API
"""

import requests
import json
from PIL import Image
import io
import os

# API base URL
BASE_URL = "http://localhost:8000"

def create_test_image():
    """Create a simple test image"""
    # Create a simple 200x200 red square image
    img = Image.new('RGB', (200, 200), color='red')
    
    # Save to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    return img_bytes

def test_api_info():
    """Test the root endpoint"""
    print("🔍 Testing API info endpoint...")
    response = requests.get(f"{BASE_URL}/")
    
    if response.status_code == 200:
        print("✅ API info endpoint working")
        print(json.dumps(response.json(), indent=2))
    else:
        print(f"❌ API info endpoint failed: {response.status_code}")
    print("-" * 50)

def test_upload():
    """Test image upload"""
    print("📤 Testing image upload...")
    
    # Create test image
    test_img = create_test_image()
    
    files = {'file': ('test_image.png', test_img, 'image/png')}
    response = requests.post(f"{BASE_URL}/upload", files=files)
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Upload successful")
        print(f"   Filename: {result['filename']}")
        print(f"   Size: {result['size']['width']}x{result['size']['height']}")
        print(f"   Format: {result['format']}")
        return result['filename']
    else:
        print(f"❌ Upload failed: {response.status_code}")
        print(response.text)
        return None
    print("-" * 50)

def test_resize():
    """Test image resize"""
    print("📏 Testing image resize...")
    
    test_img = create_test_image()
    files = {'file': ('test_image.png', test_img, 'image/png')}
    data = {
        'width': 100,
        'height': 100,
        'maintain_aspect': True
    }
    
    response = requests.post(f"{BASE_URL}/resize", files=files, data=data)
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Resize successful")
        print(f"   New size: {result['new_size']['width']}x{result['new_size']['height']}")
        return result['filename']
    else:
        print(f"❌ Resize failed: {response.status_code}")
        print(response.text)
        return None
    print("-" * 50)

def test_rotate():
    """Test image rotation"""
    print("🔄 Testing image rotation...")
    
    test_img = create_test_image()
    files = {'file': ('test_image.png', test_img, 'image/png')}
    data = {'angle': 45}
    
    response = requests.post(f"{BASE_URL}/rotate", files=files, data=data)
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Rotation successful")
        print(f"   Message: {result['message']}")
        return result['filename']
    else:
        print(f"❌ Rotation failed: {response.status_code}")
        print(response.text)
        return None
    print("-" * 50)

def test_filter():
    """Test image filter"""
    print("🎨 Testing image filter...")
    
    test_img = create_test_image()
    files = {'file': ('test_image.png', test_img, 'image/png')}
    data = {'filter_type': 'blur'}
    
    response = requests.post(f"{BASE_URL}/filter", files=files, data=data)
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Filter successful")
        print(f"   Message: {result['message']}")
        return result['filename']
    else:
        print(f"❌ Filter failed: {response.status_code}")
        print(response.text)
        return None
    print("-" * 50)

def test_enhance():
    """Test image enhancement"""
    print("✨ Testing image enhancement...")
    
    test_img = create_test_image()
    files = {'file': ('test_image.png', test_img, 'image/png')}
    data = {
        'brightness': 1.2,
        'contrast': 1.1,
        'saturation': 1.3,
        'sharpness': 1.1
    }
    
    response = requests.post(f"{BASE_URL}/enhance", files=files, data=data)
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Enhancement successful")
        print(f"   Enhancements: {result['enhancements']}")
        return result['filename']
    else:
        print(f"❌ Enhancement failed: {response.status_code}")
        print(response.text)
        return None
    print("-" * 50)

def test_crop():
    """Test image cropping"""
    print("✂️ Testing image cropping...")
    
    test_img = create_test_image()
    files = {'file': ('test_image.png', test_img, 'image/png')}
    data = {
        'left': 50,
        'top': 50,
        'right': 150,
        'bottom': 150
    }
    
    response = requests.post(f"{BASE_URL}/crop", files=files, data=data)
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Crop successful")
        print(f"   New size: {result['new_size']['width']}x{result['new_size']['height']}")
        return result['filename']
    else:
        print(f"❌ Crop failed: {response.status_code}")
        print(response.text)
        return None
    print("-" * 50)

def test_convert():
    """Test format conversion"""
    print("🔄 Testing format conversion...")
    
    test_img = create_test_image()
    files = {'file': ('test_image.png', test_img, 'image/png')}
    data = {'output_format': 'jpeg'}
    
    response = requests.post(f"{BASE_URL}/convert", files=files, data=data)
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Conversion successful")
        print(f"   Original: {result['original_format']} -> New: {result['new_format']}")
        return result['filename']
    else:
        print(f"❌ Conversion failed: {response.status_code}")
        print(response.text)
        return None
    print("-" * 50)

def test_list_images():
    """Test listing images"""
    print("📋 Testing image listing...")
    
    response = requests.get(f"{BASE_URL}/images")
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Image listing successful")
        print(f"   Uploaded images: {result['total_uploaded']}")
        print(f"   Processed images: {result['total_processed']}")
    else:
        print(f"❌ Image listing failed: {response.status_code}")
        print(response.text)
    print("-" * 50)

def test_download(filename):
    """Test downloading an image"""
    if not filename:
        print("⏭️ Skipping download test (no filename provided)")
        return
    
    print(f"⬇️ Testing image download for {filename}...")
    
    response = requests.get(f"{BASE_URL}/download/{filename}")
    
    if response.status_code == 200:
        print("✅ Download successful")
        print(f"   Content length: {len(response.content)} bytes")
        print(f"   Content type: {response.headers.get('content-type', 'unknown')}")
    else:
        print(f"❌ Download failed: {response.status_code}")
    print("-" * 50)

def main():
    """Run all tests"""
    print("🚀 Starting FastAPI Image Processing API Tests")
    print("=" * 60)
    
    # Test basic API info
    test_api_info()
    
    # Test upload and get filename for download test
    uploaded_filename = test_upload()
    
    # Test all processing operations
    resized_filename = test_resize()
    rotated_filename = test_rotate()
    filtered_filename = test_filter()
    enhanced_filename = test_enhance()
    cropped_filename = test_crop()
    converted_filename = test_convert()
    
    # Test listing images
    test_list_images()
    
    # Test download with one of the processed files
    test_download(resized_filename or uploaded_filename)
    
    print("🏁 All tests completed!")
    print("\n💡 Tips:")
    print("   - Visit http://localhost:8000/docs for interactive API documentation")
    print("   - Visit http://localhost:8000/static/index.html for the web interface")
    print("   - Check the 'uploads/' and 'processed/' directories for saved images")

if __name__ == "__main__":
    try:
        main()
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure the FastAPI server is running on http://localhost:8000")
        print("   Start the server with: python main.py")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
