from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Form
from fastapi.responses import StreamingResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional, List
import io
import os
import uuid
from PIL import Image, ImageFilter, ImageEnhance
import aiofiles
from pathlib import Path

# Create directories
os.makedirs("uploads", exist_ok=True)
os.makedirs("processed", exist_ok=True)
os.makedirs("static", exist_ok=True)

app = FastAPI(
    title="Image Processing API",
    description="A FastAPI application for uploading, editing, and processing images",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Supported image formats
SUPPORTED_FORMATS = {"image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp"}

def validate_image(file: UploadFile) -> bool:
    """Validate if uploaded file is a supported image format"""
    return file.content_type in SUPPORTED_FORMATS

def generate_filename(original_filename: str, suffix: str = "") -> str:
    """Generate unique filename"""
    file_extension = Path(original_filename).suffix
    unique_id = str(uuid.uuid4())
    return f"{unique_id}{suffix}{file_extension}"

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Image Processing API",
        "version": "1.0.0",
        "endpoints": {
            "upload": "/upload",
            "resize": "/resize",
            "rotate": "/rotate", 
            "filter": "/filter",
            "enhance": "/enhance",
            "crop": "/crop",
            "convert": "/convert",
            "list_images": "/images"
        }
    }

@app.post("/upload")
async def upload_image(file: UploadFile = File(...)):
    """Upload an image file"""
    if not validate_image(file):
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported file type. Supported formats: {', '.join(SUPPORTED_FORMATS)}"
        )
    
    # Generate unique filename
    filename = generate_filename(file.filename)
    file_path = f"uploads/{filename}"
    
    # Save uploaded file
    async with aiofiles.open(file_path, 'wb') as f:
        content = await file.read()
        await f.write(content)
    
    # Get image info
    with Image.open(file_path) as img:
        width, height = img.size
        format_type = img.format
    
    return {
        "message": "Image uploaded successfully",
        "filename": filename,
        "original_filename": file.filename,
        "size": {"width": width, "height": height},
        "format": format_type,
        "file_size": len(content),
        "download_url": f"/download/{filename}"
    }

@app.post("/resize")
async def resize_image(
    file: UploadFile = File(...),
    width: int = Form(...),
    height: int = Form(...),
    maintain_aspect: bool = Form(default=True)
):
    """Resize an image to specified dimensions"""
    if not validate_image(file):
        raise HTTPException(status_code=400, detail="Unsupported file type")
    
    # Process image
    content = await file.read()
    img = Image.open(io.BytesIO(content))
    
    if maintain_aspect:
        img.thumbnail((width, height), Image.Resampling.LANCZOS)
    else:
        img = img.resize((width, height), Image.Resampling.LANCZOS)
    
    # Save processed image
    filename = generate_filename(file.filename, "_resized")
    output_path = f"processed/{filename}"
    img.save(output_path)
    
    return {
        "message": "Image resized successfully",
        "filename": filename,
        "new_size": {"width": img.width, "height": img.height},
        "download_url": f"/download/{filename}"
    }

@app.post("/rotate")
async def rotate_image(
    file: UploadFile = File(...),
    angle: float = Form(...)
):
    """Rotate an image by specified angle"""
    if not validate_image(file):
        raise HTTPException(status_code=400, detail="Unsupported file type")
    
    content = await file.read()
    img = Image.open(io.BytesIO(content))
    
    # Rotate image
    rotated_img = img.rotate(angle, expand=True, fillcolor='white')
    
    # Save processed image
    filename = generate_filename(file.filename, f"_rotated_{angle}")
    output_path = f"processed/{filename}"
    rotated_img.save(output_path)
    
    return {
        "message": f"Image rotated by {angle} degrees",
        "filename": filename,
        "download_url": f"/download/{filename}"
    }

@app.post("/filter")
async def apply_filter(
    file: UploadFile = File(...),
    filter_type: str = Form(...)
):
    """Apply various filters to an image"""
    if not validate_image(file):
        raise HTTPException(status_code=400, detail="Unsupported file type")
    
    # Available filters
    filters = {
        "blur": ImageFilter.BLUR,
        "contour": ImageFilter.CONTOUR,
        "detail": ImageFilter.DETAIL,
        "edge_enhance": ImageFilter.EDGE_ENHANCE,
        "emboss": ImageFilter.EMBOSS,
        "find_edges": ImageFilter.FIND_EDGES,
        "sharpen": ImageFilter.SHARPEN,
        "smooth": ImageFilter.SMOOTH,
        "gaussian_blur": ImageFilter.GaussianBlur(radius=2)
    }
    
    if filter_type not in filters:
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported filter. Available filters: {', '.join(filters.keys())}"
        )
    
    content = await file.read()
    img = Image.open(io.BytesIO(content))
    
    # Apply filter
    filtered_img = img.filter(filters[filter_type])
    
    # Save processed image
    filename = generate_filename(file.filename, f"_{filter_type}")
    output_path = f"processed/{filename}"
    filtered_img.save(output_path)
    
    return {
        "message": f"Applied {filter_type} filter",
        "filename": filename,
        "download_url": f"/download/{filename}"
    }

@app.post("/enhance")
async def enhance_image(
    file: UploadFile = File(...),
    brightness: float = Form(default=1.0),
    contrast: float = Form(default=1.0),
    saturation: float = Form(default=1.0),
    sharpness: float = Form(default=1.0)
):
    """Enhance image properties (brightness, contrast, saturation, sharpness)"""
    if not validate_image(file):
        raise HTTPException(status_code=400, detail="Unsupported file type")
    
    content = await file.read()
    img = Image.open(io.BytesIO(content))
    
    # Apply enhancements
    if brightness != 1.0:
        enhancer = ImageEnhance.Brightness(img)
        img = enhancer.enhance(brightness)
    
    if contrast != 1.0:
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(contrast)
    
    if saturation != 1.0:
        enhancer = ImageEnhance.Color(img)
        img = enhancer.enhance(saturation)
    
    if sharpness != 1.0:
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(sharpness)
    
    # Save processed image
    filename = generate_filename(file.filename, "_enhanced")
    output_path = f"processed/{filename}"
    img.save(output_path)
    
    return {
        "message": "Image enhanced successfully",
        "filename": filename,
        "enhancements": {
            "brightness": brightness,
            "contrast": contrast,
            "saturation": saturation,
            "sharpness": sharpness
        },
        "download_url": f"/download/{filename}"
    }

@app.post("/crop")
async def crop_image(
    file: UploadFile = File(...),
    left: int = Form(...),
    top: int = Form(...),
    right: int = Form(...),
    bottom: int = Form(...)
):
    """Crop an image to specified coordinates"""
    if not validate_image(file):
        raise HTTPException(status_code=400, detail="Unsupported file type")
    
    content = await file.read()
    img = Image.open(io.BytesIO(content))
    
    # Validate crop coordinates
    if left >= right or top >= bottom:
        raise HTTPException(status_code=400, detail="Invalid crop coordinates")
    
    if right > img.width or bottom > img.height:
        raise HTTPException(status_code=400, detail="Crop coordinates exceed image dimensions")
    
    # Crop image
    cropped_img = img.crop((left, top, right, bottom))
    
    # Save processed image
    filename = generate_filename(file.filename, "_cropped")
    output_path = f"processed/{filename}"
    cropped_img.save(output_path)
    
    return {
        "message": "Image cropped successfully",
        "filename": filename,
        "crop_area": {"left": left, "top": top, "right": right, "bottom": bottom},
        "new_size": {"width": cropped_img.width, "height": cropped_img.height},
        "download_url": f"/download/{filename}"
    }

@app.post("/convert")
async def convert_format(
    file: UploadFile = File(...),
    output_format: str = Form(...)
):
    """Convert image to different format"""
    if not validate_image(file):
        raise HTTPException(status_code=400, detail="Unsupported file type")

    # Supported output formats
    format_mapping = {
        "jpeg": "JPEG",
        "jpg": "JPEG",
        "png": "PNG",
        "gif": "GIF",
        "bmp": "BMP",
        "webp": "WEBP"
    }

    output_format = output_format.lower()
    if output_format not in format_mapping:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported output format. Supported: {', '.join(format_mapping.keys())}"
        )

    content = await file.read()
    img = Image.open(io.BytesIO(content))

    # Convert RGBA to RGB for JPEG
    if format_mapping[output_format] == "JPEG" and img.mode in ("RGBA", "P"):
        img = img.convert("RGB")

    # Generate filename with new extension
    base_name = Path(file.filename).stem
    new_filename = f"{str(uuid.uuid4())}_{base_name}_converted.{output_format}"
    output_path = f"processed/{new_filename}"

    # Save in new format
    img.save(output_path, format=format_mapping[output_format])

    return {
        "message": f"Image converted to {output_format.upper()}",
        "filename": new_filename,
        "original_format": img.format,
        "new_format": format_mapping[output_format],
        "download_url": f"/download/{new_filename}"
    }

@app.get("/download/{filename}")
async def download_image(filename: str):
    """Download processed image"""
    # Check in both uploads and processed directories
    file_paths = [f"uploads/{filename}", f"processed/{filename}"]

    for file_path in file_paths:
        if os.path.exists(file_path):
            return FileResponse(
                file_path,
                media_type="application/octet-stream",
                filename=filename
            )

    raise HTTPException(status_code=404, detail="File not found")

@app.get("/images")
async def list_images():
    """List all uploaded and processed images"""
    uploaded_images = []
    processed_images = []

    # List uploaded images
    if os.path.exists("uploads"):
        for filename in os.listdir("uploads"):
            file_path = f"uploads/{filename}"
            if os.path.isfile(file_path):
                stat = os.stat(file_path)
                uploaded_images.append({
                    "filename": filename,
                    "size": stat.st_size,
                    "created": stat.st_ctime,
                    "download_url": f"/download/{filename}"
                })

    # List processed images
    if os.path.exists("processed"):
        for filename in os.listdir("processed"):
            file_path = f"processed/{filename}"
            if os.path.isfile(file_path):
                stat = os.stat(file_path)
                processed_images.append({
                    "filename": filename,
                    "size": stat.st_size,
                    "created": stat.st_ctime,
                    "download_url": f"/download/{filename}"
                })

    return {
        "uploaded_images": uploaded_images,
        "processed_images": processed_images,
        "total_uploaded": len(uploaded_images),
        "total_processed": len(processed_images)
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
