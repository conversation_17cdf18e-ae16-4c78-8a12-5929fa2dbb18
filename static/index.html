<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Processing API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, button {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        input[type="file"] {
            width: 100%;
        }
        input[type="number"], input[type="text"], select {
            width: 200px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .image-preview {
            max-width: 300px;
            max-height: 300px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Image Processing API</h1>
        
        <!-- Upload Section -->
        <div class="section">
            <h2>📤 Upload Image</h2>
            <form id="uploadForm">
                <div class="form-group">
                    <label for="uploadFile">Select Image:</label>
                    <input type="file" id="uploadFile" accept="image/*" required>
                </div>
                <button type="submit">Upload Image</button>
            </form>
            <div id="uploadResult" class="result"></div>
        </div>

        <!-- Resize Section -->
        <div class="section">
            <h2>📏 Resize Image</h2>
            <form id="resizeForm">
                <div class="form-group">
                    <label for="resizeFile">Select Image:</label>
                    <input type="file" id="resizeFile" accept="image/*" required>
                </div>
                <div class="form-group">
                    <label for="width">Width:</label>
                    <input type="number" id="width" value="800" required>
                </div>
                <div class="form-group">
                    <label for="height">Height:</label>
                    <input type="number" id="height" value="600" required>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="maintainAspect" checked>
                        Maintain Aspect Ratio
                    </label>
                </div>
                <button type="submit">Resize Image</button>
            </form>
            <div id="resizeResult" class="result"></div>
        </div>

        <!-- Rotate Section -->
        <div class="section">
            <h2>🔄 Rotate Image</h2>
            <form id="rotateForm">
                <div class="form-group">
                    <label for="rotateFile">Select Image:</label>
                    <input type="file" id="rotateFile" accept="image/*" required>
                </div>
                <div class="form-group">
                    <label for="angle">Angle (degrees):</label>
                    <input type="number" id="angle" value="90" required>
                </div>
                <button type="submit">Rotate Image</button>
            </form>
            <div id="rotateResult" class="result"></div>
        </div>

        <!-- Filter Section -->
        <div class="section">
            <h2>🎨 Apply Filter</h2>
            <form id="filterForm">
                <div class="form-group">
                    <label for="filterFile">Select Image:</label>
                    <input type="file" id="filterFile" accept="image/*" required>
                </div>
                <div class="form-group">
                    <label for="filterType">Filter Type:</label>
                    <select id="filterType" required>
                        <option value="blur">Blur</option>
                        <option value="contour">Contour</option>
                        <option value="detail">Detail</option>
                        <option value="edge_enhance">Edge Enhance</option>
                        <option value="emboss">Emboss</option>
                        <option value="find_edges">Find Edges</option>
                        <option value="sharpen">Sharpen</option>
                        <option value="smooth">Smooth</option>
                        <option value="gaussian_blur">Gaussian Blur</option>
                    </select>
                </div>
                <button type="submit">Apply Filter</button>
            </form>
            <div id="filterResult" class="result"></div>
        </div>

        <!-- Enhance Section -->
        <div class="section">
            <h2>✨ Enhance Image</h2>
            <form id="enhanceForm">
                <div class="form-group">
                    <label for="enhanceFile">Select Image:</label>
                    <input type="file" id="enhanceFile" accept="image/*" required>
                </div>
                <div class="form-group">
                    <label for="brightness">Brightness (0.5-2.0):</label>
                    <input type="number" id="brightness" value="1.0" step="0.1" min="0.1" max="3.0">
                </div>
                <div class="form-group">
                    <label for="contrast">Contrast (0.5-2.0):</label>
                    <input type="number" id="contrast" value="1.0" step="0.1" min="0.1" max="3.0">
                </div>
                <div class="form-group">
                    <label for="saturation">Saturation (0.5-2.0):</label>
                    <input type="number" id="saturation" value="1.0" step="0.1" min="0.1" max="3.0">
                </div>
                <div class="form-group">
                    <label for="sharpness">Sharpness (0.5-2.0):</label>
                    <input type="number" id="sharpness" value="1.0" step="0.1" min="0.1" max="3.0">
                </div>
                <button type="submit">Enhance Image</button>
            </form>
            <div id="enhanceResult" class="result"></div>
        </div>

        <!-- Convert Section -->
        <div class="section">
            <h2>🔄 Convert Format</h2>
            <form id="convertForm">
                <div class="form-group">
                    <label for="convertFile">Select Image:</label>
                    <input type="file" id="convertFile" accept="image/*" required>
                </div>
                <div class="form-group">
                    <label for="outputFormat">Output Format:</label>
                    <select id="outputFormat" required>
                        <option value="jpeg">JPEG</option>
                        <option value="png">PNG</option>
                        <option value="gif">GIF</option>
                        <option value="bmp">BMP</option>
                        <option value="webp">WebP</option>
                    </select>
                </div>
                <button type="submit">Convert Image</button>
            </form>
            <div id="convertResult" class="result"></div>
        </div>

        <!-- List Images Section -->
        <div class="section">
            <h2>📋 List Images</h2>
            <button onclick="listImages()">Refresh Image List</button>
            <div id="imageList" class="result"></div>
        </div>
    </div>

    <script>
        // Helper function to show results
        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.style.display = 'block';
        }

        // Helper function to create download link
        function createDownloadLink(filename) {
            return `<a href="/download/${filename}" target="_blank">Download ${filename}</a>`;
        }

        // Upload form handler
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData();
            const file = document.getElementById('uploadFile').files[0];
            formData.append('file', file);

            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                
                if (response.ok) {
                    showResult('uploadResult', 
                        `✅ ${result.message}\nFilename: ${result.filename}\nSize: ${result.size.width}x${result.size.height}\n${createDownloadLink(result.filename)}`);
                } else {
                    showResult('uploadResult', `❌ Error: ${result.detail}`, true);
                }
            } catch (error) {
                showResult('uploadResult', `❌ Error: ${error.message}`, true);
            }
        });

        // Resize form handler
        document.getElementById('resizeForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData();
            const file = document.getElementById('resizeFile').files[0];
            formData.append('file', file);
            formData.append('width', document.getElementById('width').value);
            formData.append('height', document.getElementById('height').value);
            formData.append('maintain_aspect', document.getElementById('maintainAspect').checked);

            try {
                const response = await fetch('/resize', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                
                if (response.ok) {
                    showResult('resizeResult', 
                        `✅ ${result.message}\nNew size: ${result.new_size.width}x${result.new_size.height}\n${createDownloadLink(result.filename)}`);
                } else {
                    showResult('resizeResult', `❌ Error: ${result.detail}`, true);
                }
            } catch (error) {
                showResult('resizeResult', `❌ Error: ${error.message}`, true);
            }
        });

        // Rotate form handler
        document.getElementById('rotateForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData();
            const file = document.getElementById('rotateFile').files[0];
            formData.append('file', file);
            formData.append('angle', document.getElementById('angle').value);

            try {
                const response = await fetch('/rotate', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                
                if (response.ok) {
                    showResult('rotateResult', 
                        `✅ ${result.message}\n${createDownloadLink(result.filename)}`);
                } else {
                    showResult('rotateResult', `❌ Error: ${result.detail}`, true);
                }
            } catch (error) {
                showResult('rotateResult', `❌ Error: ${error.message}`, true);
            }
        });

        // Filter form handler
        document.getElementById('filterForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData();
            const file = document.getElementById('filterFile').files[0];
            formData.append('file', file);
            formData.append('filter_type', document.getElementById('filterType').value);

            try {
                const response = await fetch('/filter', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                
                if (response.ok) {
                    showResult('filterResult', 
                        `✅ ${result.message}\n${createDownloadLink(result.filename)}`);
                } else {
                    showResult('filterResult', `❌ Error: ${result.detail}`, true);
                }
            } catch (error) {
                showResult('filterResult', `❌ Error: ${error.message}`, true);
            }
        });

        // Enhance form handler
        document.getElementById('enhanceForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData();
            const file = document.getElementById('enhanceFile').files[0];
            formData.append('file', file);
            formData.append('brightness', document.getElementById('brightness').value);
            formData.append('contrast', document.getElementById('contrast').value);
            formData.append('saturation', document.getElementById('saturation').value);
            formData.append('sharpness', document.getElementById('sharpness').value);

            try {
                const response = await fetch('/enhance', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                
                if (response.ok) {
                    showResult('enhanceResult', 
                        `✅ ${result.message}\n${createDownloadLink(result.filename)}`);
                } else {
                    showResult('enhanceResult', `❌ Error: ${result.detail}`, true);
                }
            } catch (error) {
                showResult('enhanceResult', `❌ Error: ${error.message}`, true);
            }
        });

        // Convert form handler
        document.getElementById('convertForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData();
            const file = document.getElementById('convertFile').files[0];
            formData.append('file', file);
            formData.append('output_format', document.getElementById('outputFormat').value);

            try {
                const response = await fetch('/convert', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                
                if (response.ok) {
                    showResult('convertResult', 
                        `✅ ${result.message}\n${createDownloadLink(result.filename)}`);
                } else {
                    showResult('convertResult', `❌ Error: ${result.detail}`, true);
                }
            } catch (error) {
                showResult('convertResult', `❌ Error: ${error.message}`, true);
            }
        });

        // List images function
        async function listImages() {
            try {
                const response = await fetch('/images');
                const result = await response.json();
                
                if (response.ok) {
                    let html = `<h3>📤 Uploaded Images (${result.total_uploaded})</h3>`;
                    result.uploaded_images.forEach(img => {
                        html += `<p>📄 ${img.filename} (${img.size} bytes) - ${createDownloadLink(img.filename)}</p>`;
                    });
                    
                    html += `<h3>⚙️ Processed Images (${result.total_processed})</h3>`;
                    result.processed_images.forEach(img => {
                        html += `<p>🔧 ${img.filename} (${img.size} bytes) - ${createDownloadLink(img.filename)}</p>`;
                    });
                    
                    document.getElementById('imageList').innerHTML = html;
                    document.getElementById('imageList').className = 'result success';
                    document.getElementById('imageList').style.display = 'block';
                } else {
                    showResult('imageList', `❌ Error: ${result.detail}`, true);
                }
            } catch (error) {
                showResult('imageList', `❌ Error: ${error.message}`, true);
            }
        }
    </script>
</body>
</html>
