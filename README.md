# 🖼️ FastAPI Image Processing API

A comprehensive FastAPI application for uploading, editing, and processing images with various operations like resizing, rotating, filtering, enhancing, cropping, and format conversion.

## ✨ Features

- **📤 Image Upload**: Upload images in various formats (JPEG, PNG, GIF, BMP, WebP)
- **📏 Image Resizing**: Resize images with optional aspect ratio maintenance
- **🔄 Image Rotation**: Rotate images by any angle
- **🎨 Image Filters**: Apply various filters (blur, sharpen, emboss, edge detection, etc.)
- **✨ Image Enhancement**: Adjust brightness, contrast, saturation, and sharpness
- **✂️ Image Cropping**: Crop images to specified coordinates
- **🔄 Format Conversion**: Convert between different image formats
- **📋 Image Management**: List and download processed images
- **🌐 Web Interface**: Simple HTML interface for testing all features

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- pip

### Installation

1. **Clone or create the project directory:**
```bash
mkdir fastapi-image-processor
cd fastapi-image-processor
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Run the application:**
```bash
python main.py
```

Or using uvicorn directly:
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

4. **Access the application:**
- **API Documentation**: http://localhost:8000/docs
- **Web Interface**: http://localhost:8000/static/index.html
- **Alternative API Docs**: http://localhost:8000/redoc

## 📚 API Endpoints

### Core Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/` | API information and available endpoints |
| `POST` | `/upload` | Upload an image file |
| `GET` | `/images` | List all uploaded and processed images |
| `GET` | `/download/{filename}` | Download a specific image |

### Image Processing Endpoints

| Method | Endpoint | Description | Parameters |
|--------|----------|-------------|------------|
| `POST` | `/resize` | Resize image | `file`, `width`, `height`, `maintain_aspect` |
| `POST` | `/rotate` | Rotate image | `file`, `angle` |
| `POST` | `/filter` | Apply filter | `file`, `filter_type` |
| `POST` | `/enhance` | Enhance image | `file`, `brightness`, `contrast`, `saturation`, `sharpness` |
| `POST` | `/crop` | Crop image | `file`, `left`, `top`, `right`, `bottom` |
| `POST` | `/convert` | Convert format | `file`, `output_format` |

## 🎨 Available Filters

- `blur` - Basic blur effect
- `contour` - Contour detection
- `detail` - Detail enhancement
- `edge_enhance` - Edge enhancement
- `emboss` - Emboss effect
- `find_edges` - Edge detection
- `sharpen` - Sharpen image
- `smooth` - Smooth image
- `gaussian_blur` - Gaussian blur with radius=2

## 🔧 Usage Examples

### Using cURL

**Upload an image:**
```bash
curl -X POST "http://localhost:8000/upload" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@your_image.jpg"
```

**Resize an image:**
```bash
curl -X POST "http://localhost:8000/resize" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@your_image.jpg" \
     -F "width=800" \
     -F "height=600" \
     -F "maintain_aspect=true"
```

**Apply a filter:**
```bash
curl -X POST "http://localhost:8000/filter" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@your_image.jpg" \
     -F "filter_type=sharpen"
```

### Using Python requests

```python
import requests

# Upload image
with open('your_image.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/upload',
        files={'file': f}
    )
    print(response.json())

# Resize image
with open('your_image.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/resize',
        files={'file': f},
        data={
            'width': 800,
            'height': 600,
            'maintain_aspect': True
        }
    )
    print(response.json())
```

## 📁 Project Structure

```
fastapi-image-processor/
├── main.py                 # Main FastAPI application
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── static/
│   └── index.html         # Web interface
├── uploads/               # Uploaded images (created automatically)
└── processed/             # Processed images (created automatically)
```

## 🛠️ Dependencies

- **FastAPI**: Modern web framework for building APIs
- **Uvicorn**: ASGI server for running FastAPI
- **Pillow (PIL)**: Python Imaging Library for image processing
- **aiofiles**: Async file operations
- **python-multipart**: For handling file uploads

## 🔒 Security Considerations

- File type validation to prevent malicious uploads
- Unique filename generation to prevent conflicts
- File size limitations (can be configured)
- Input validation for all parameters

## 🚀 Deployment

### Using Docker (Optional)

Create a `Dockerfile`:
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

Build and run:
```bash
docker build -t fastapi-image-processor .
docker run -p 8000:8000 fastapi-image-processor
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is open source and available under the MIT License.

## 🐛 Troubleshooting

**Common Issues:**

1. **Import errors**: Make sure all dependencies are installed
2. **Permission errors**: Ensure the application has write permissions for uploads/ and processed/ directories
3. **Large file uploads**: Adjust FastAPI's file size limits if needed
4. **Memory issues**: For large images, consider implementing streaming or chunked processing

## 📞 Support

For issues and questions:
- Check the FastAPI documentation: https://fastapi.tiangolo.com/
- Review Pillow documentation: https://pillow.readthedocs.io/
- Open an issue in the project repository
